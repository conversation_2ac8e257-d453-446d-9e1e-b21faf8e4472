# Lộ trình học Node.js từ cơ bản đến nâng cao

## 📋 Tổng quan

Node.js là một runtime environment cho phép chạy JavaScript trên server-side. Lộ trình này sẽ giúp bạn từ người mới bắt đầu trở thành developer Node.js thành thạo.

## 🎯 Mục tiêu học tập

- <PERSON><PERSON><PERSON> cơ bản về Node.js và JavaScript backend
- Xây dựng RESTful APIs
- Làm việc với databases
- Triển khai ứng dụng production-ready
- Hiểu về microservices và scalability

## 📚 Giai đoạn 1: <PERSON><PERSON> bản (4-6 tuần)

### Tuần 1-2: JavaScript Foundation

**Kiến thức cần có:**

- [ ] ES6+ features (arrow functions, destructuring, async/await)
- [ ] Promises và async programming
- [ ] Modules (import/export)
- [ ] Error handling (try/catch)

**Tài liệu:**

- MDN JavaScript Guide
- JavaScript.info
- "You Don't Know JS" series

### Tuần 3-4: Node.js Basics

**Nội dung học:**

- [ ] Cài đặt Node.js và npm
- [ ] Node.js architecture và Event Loop
- [ ] Core modules: fs, path, os, url
- [ ] NPM và package management
- [ ] Tạo first Node.js application

**Thực hành:**

```javascript
// Hello World server
const http = require("http");

const server = http.createServer((req, res) => {
  res.writeHead(200, { "Content-Type": "text/plain" });
  res.end("Hello World!");
});

server.listen(3000, () => {
  console.log("Server running on port 3000");
});
```

### Tuần 5-6: File System & Streams

**Nội dung học:**

- [ ] File operations (read, write, delete)
- [ ] Streams và Buffers
- [ ] Event Emitters
- [ ] Working with JSON

**Project:** File manager CLI tool

## 📚 Giai đoạn 2: Web Development (6-8 tuần)

### Tuần 7-8: HTTP & Express.js

**Nội dung học:**

- [ ] HTTP protocol basics
- [ ] Express.js framework
- [ ] Routing và middleware
- [ ] Request/Response handling
- [ ] Static files serving

**Thực hành:**

```javascript
const express = require("express");
const app = express();

app.use(express.json());

app.get("/api/users", (req, res) => {
  res.json({ message: "Get all users" });
});

app.listen(3000, () => {
  console.log("Server running on port 3000");
});
```

### Tuần 9-10: RESTful APIs

**Nội dung học:**

- [ ] REST principles
- [ ] CRUD operations
- [ ] Status codes
- [ ] API design best practices
- [ ] Postman testing

**Project:** Todo API với full CRUD

### Tuần 11-12: Middleware & Authentication

**Nội dung học:**

- [ ] Custom middleware
- [ ] Error handling middleware
- [ ] CORS
- [ ] JWT authentication
- [ ] Password hashing (bcrypt)

### Tuần 13-14: Database Integration

**Nội dung học:**

- [ ] MongoDB với Mongoose
- [ ] PostgreSQL với Sequelize/Prisma
- [ ] Database design
- [ ] Migrations và seeding

**Thực hành:**

```javascript
// Mongoose example
const mongoose = require("mongoose");

const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
});

module.exports = mongoose.model("User", userSchema);
```

## 📚 Giai đoạn 3: Nâng cao (8-10 tuần)

### Tuần 15-16: Testing

**Nội dung học:**

- [ ] Unit testing với Jest
- [ ] Integration testing
- [ ] API testing với Supertest
- [ ] Test-driven development (TDD)
- [ ] Mocking và stubbing

### Tuần 17-18: Security

**Nội dung học:**

- [ ] Common vulnerabilities (OWASP Top 10)
- [ ] Input validation
- [ ] Rate limiting
- [ ] Helmet.js
- [ ] Environment variables

### Tuần 19-20: Performance & Optimization

**Nội dung học:**

- [ ] Profiling và monitoring
- [ ] Caching strategies (Redis)
- [ ] Database optimization
- [ ] Memory management
- [ ] Clustering

### Tuần 21-22: Real-time Applications

**Nội dung học:**

- [ ] WebSockets
- [ ] Socket.io
- [ ] Server-Sent Events
- [ ] Real-time chat application

### Tuần 23-24: Deployment & DevOps

**Nội dung học:**

- [ ] Environment setup
- [ ] Docker containerization
- [ ] CI/CD pipelines
- [ ] Cloud deployment (AWS, Heroku, DigitalOcean)
- [ ] Process managers (PM2)

## 📚 Giai đoạn 4: Chuyên sâu (6-8 tuần)

### Tuần 25-26: Microservices

**Nội dung học:**

- [ ] Microservices architecture
- [ ] API Gateway
- [ ] Service discovery
- [ ] Message queues (RabbitMQ, Apache Kafka)

### Tuần 27-28: GraphQL

**Nội dung học:**

- [ ] GraphQL basics
- [ ] Apollo Server
- [ ] Schema design
- [ ] Resolvers và data loading

### Tuần 29-30: Advanced Topics

**Nội dung học:**

- [ ] TypeScript với Node.js
- [ ] Serverless functions
- [ ] Event-driven architecture
- [ ] Design patterns

### Tuần 31-32: Final Project

**Project lớn:**

- E-commerce platform hoặc Social media API
- Áp dụng tất cả kiến thức đã học
- Production-ready deployment

## 🛠️ Tools & Technologies

### Essential Tools:

- **Code Editor:** VS Code
- **Version Control:** Git & GitHub
- **API Testing:** Postman/Insomnia
- **Database:** MongoDB, PostgreSQL
- **Caching:** Redis
- **Containerization:** Docker

### Recommended Packages:

```json
{
  "dependencies": {
    "express": "^4.18.0",
    "mongoose": "^6.0.0",
    "jsonwebtoken": "^8.5.0",
    "bcryptjs": "^2.4.0",
    "cors": "^2.8.0",
    "helmet": "^5.0.0",
    "dotenv": "^16.0.0"
  },
  "devDependencies": {
    "jest": "^28.0.0",
    "supertest": "^6.2.0",
    "nodemon": "^2.0.0"
  }
}
```

## 📖 Tài liệu tham khảo

### Books:

1. "Node.js Design Patterns" - Mario Casciaro
2. "Learning Node.js" - Marc Wandschneider
3. "Node.js in Action" - Manning Publications

### Online Resources:

- [Node.js Official Documentation](https://nodejs.org/docs/)
- [Express.js Guide](https://expressjs.com/)
- [MongoDB University](https://university.mongodb.com/)
- [FreeCodeCamp Node.js Course](https://www.freecodecamp.org/)

### YouTube Channels:

- Traversy Media
- The Net Ninja
- Academind

## 🎯 Milestones & Checkpoints

### Milestone 1 (Tuần 6): Basic Node.js

- [ ] Tạo được simple HTTP server
- [ ] Làm việc với file system
- [ ] Hiểu Event Loop

### Milestone 2 (Tuần 14): Web APIs

- [ ] Xây dựng RESTful API hoàn chỉnh
- [ ] Implement authentication
- [ ] Database integration

### Milestone 3 (Tuần 24): Production Ready

- [ ] Testing coverage > 80%
- [ ] Security best practices
- [ ] Deployed application

### Milestone 4 (Tuần 32): Advanced Developer

- [ ] Microservices project
- [ ] Performance optimization
- [ ] Complete portfolio

## 💡 Tips thành công

1. **Thực hành hàng ngày:** Code ít nhất 1-2 giờ mỗi ngày
2. **Build projects:** Áp dụng kiến thức vào projects thực tế
3. **Join community:** Tham gia Node.js communities, forums
4. **Read code:** Đọc source code của popular packages
5. **Stay updated:** Follow Node.js releases và best practices

## 🚀 Next Steps

Sau khi hoàn thành lộ trình này, bạn có thể:

- Chuyên sâu vào specific domains (IoT, AI/ML với Node.js)
- Học frameworks khác (NestJS, Fastify)
- Contribute to open source projects
- Mentor người khác học Node.js

---

**Thời gian ước tính:** 8-10 tháng (part-time)
**Cập nhật lần cuối:** Tháng 6, 2025
